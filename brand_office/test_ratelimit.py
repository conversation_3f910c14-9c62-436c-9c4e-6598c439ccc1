#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra ratelimit mới của Gemini
Mục tiêu: Đạt được 2000 RPM
"""

import asyncio
import time
import logging
from gemini import Gemini

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/test_ratelimit.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RateLimitTester:
    def __init__(self, target_rpm=2000, test_duration_minutes=2):
        self.target_rpm = target_rpm
        self.test_duration_minutes = test_duration_minutes
        self.gemini = Gemini(rate_limit_rpm=target_rpm, max_concurrent=2000)
        
    async def create_test_data(self, count=1000):
        """Tạo test data giả lập"""
        test_data = []
        for i in range(count):
            test_data.append({
                'brand_office': {
                    'id': f'test_{i}',
                    'address_old': f'<PERSON><PERSON> {i}, Đường Test {i % 10}, <PERSON><PERSON><PERSON>n {i % 12}, TP Test'
                },
                'ward': f'Phường Test {i % 20}',
                'province': f'Tỉnh Test {i % 5}'
            })
        return test_data
    
    async def test_single_request(self, data):
        """Test một request đơn lẻ"""
        try:
            initial_prompt = self.gemini.build_conversion_prompt()
            result = await self.gemini.convert_address_async(
                initial_prompt,
                data['brand_office'],
                data['ward'],
                data['province']
            )
            return {'success': True, 'result': result}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_batch_requests(self, test_data, batch_size=100):
        """Test batch requests"""
        logger.info(f"🚀 Testing batch requests: {len(test_data)} requests, batch_size: {batch_size}")
        
        start_time = time.time()
        results = []
        
        # Process in batches
        for i in range(0, len(test_data), batch_size):
            batch = test_data[i:i + batch_size]
            batch_start = time.time()
            
            # Create tasks for concurrent execution
            tasks = [self.test_single_request(data) for data in batch]
            
            # Execute batch
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            results.extend(batch_results)
            
            batch_time = time.time() - batch_start
            batch_rpm = len(batch) / (batch_time / 60) if batch_time > 0 else 0
            
            logger.info(f"📊 Batch {i//batch_size + 1}: {len(batch)} requests in {batch_time:.2f}s ({batch_rpm:.1f} RPM)")
        
        total_time = time.time() - start_time
        total_rpm = len(test_data) / (total_time / 60) if total_time > 0 else 0
        
        # Analyze results
        successful = len([r for r in results if isinstance(r, dict) and r.get('success', False)])
        failed = len(results) - successful
        
        logger.info("="*60)
        logger.info("📊 BATCH TEST RESULTS")
        logger.info("="*60)
        logger.info(f"Total Requests: {len(test_data)}")
        logger.info(f"Successful: {successful} ({successful/len(results)*100:.1f}%)")
        logger.info(f"Failed: {failed} ({failed/len(results)*100:.1f}%)")
        logger.info(f"Total Time: {total_time:.2f}s ({total_time/60:.2f} minutes)")
        logger.info(f"Actual RPM: {total_rpm:.1f}")
        logger.info(f"Target RPM: {self.target_rpm}")
        logger.info(f"Efficiency: {total_rpm/self.target_rpm*100:.1f}%")
        logger.info("="*60)
        
        return {
            'total_requests': len(test_data),
            'successful': successful,
            'failed': failed,
            'total_time': total_time,
            'actual_rpm': total_rpm,
            'target_rpm': self.target_rpm,
            'efficiency': total_rpm/self.target_rpm*100
        }
    
    async def test_sustained_load(self):
        """Test sustained load trong thời gian dài"""
        logger.info(f"🚀 Testing sustained load for {self.test_duration_minutes} minutes")
        
        end_time = time.time() + (self.test_duration_minutes * 60)
        request_count = 0
        success_count = 0
        start_time = time.time()
        
        # Create test data
        test_data = await self.create_test_data(5000)  # Large pool
        
        while time.time() < end_time:
            # Pick random data
            data = test_data[request_count % len(test_data)]
            
            try:
                result = await self.test_single_request(data)
                if result.get('success', False):
                    success_count += 1
                request_count += 1
                
                # Log progress every 100 requests
                if request_count % 100 == 0:
                    elapsed = time.time() - start_time
                    current_rpm = request_count / (elapsed / 60) if elapsed > 0 else 0
                    logger.info(f"📊 Sustained: {request_count} requests | {current_rpm:.1f} RPM | {success_count/request_count*100:.1f}% success")
                    
            except Exception as e:
                logger.error(f"❌ Error in sustained test: {e}")
                request_count += 1
        
        total_time = time.time() - start_time
        actual_rpm = request_count / (total_time / 60) if total_time > 0 else 0
        
        logger.info("="*60)
        logger.info("📊 SUSTAINED LOAD TEST RESULTS")
        logger.info("="*60)
        logger.info(f"Duration: {total_time/60:.2f} minutes")
        logger.info(f"Total Requests: {request_count}")
        logger.info(f"Successful: {success_count} ({success_count/request_count*100:.1f}%)")
        logger.info(f"Actual RPM: {actual_rpm:.1f}")
        logger.info(f"Target RPM: {self.target_rpm}")
        logger.info(f"Efficiency: {actual_rpm/self.target_rpm*100:.1f}%")
        logger.info("="*60)
        
        return {
            'duration_minutes': total_time / 60,
            'total_requests': request_count,
            'successful': success_count,
            'actual_rpm': actual_rpm,
            'target_rpm': self.target_rpm,
            'efficiency': actual_rpm/self.target_rpm*100
        }
    
    async def run_all_tests(self):
        """Chạy tất cả các tests"""
        logger.info("🚀 STARTING RATELIMIT TESTS")
        logger.info("="*60)
        
        # Test 1: Small batch
        logger.info("📋 Test 1: Small batch (100 requests)")
        test_data_small = await self.create_test_data(100)
        small_results = await self.test_batch_requests(test_data_small, batch_size=50)
        
        await asyncio.sleep(5)  # Cool down
        
        # Test 2: Medium batch  
        logger.info("📋 Test 2: Medium batch (500 requests)")
        test_data_medium = await self.create_test_data(500)
        medium_results = await self.test_batch_requests(test_data_medium, batch_size=100)
        
        await asyncio.sleep(5)  # Cool down
        
        # Test 3: Sustained load
        logger.info("📋 Test 3: Sustained load")
        sustained_results = await self.test_sustained_load()
        
        # Final Gemini stats
        self.gemini.log_final_stats()
        
        # Summary
        logger.info("="*60)
        logger.info("📊 FINAL TEST SUMMARY")
        logger.info("="*60)
        logger.info(f"Small Batch: {small_results['actual_rpm']:.1f} RPM ({small_results['efficiency']:.1f}%)")
        logger.info(f"Medium Batch: {medium_results['actual_rpm']:.1f} RPM ({medium_results['efficiency']:.1f}%)")
        logger.info(f"Sustained Load: {sustained_results['actual_rpm']:.1f} RPM ({sustained_results['efficiency']:.1f}%)")
        logger.info(f"Target: {self.target_rpm} RPM")
        logger.info("="*60)

async def main():
    """Main function"""
    tester = RateLimitTester(target_rpm=2000, test_duration_minutes=1)
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
