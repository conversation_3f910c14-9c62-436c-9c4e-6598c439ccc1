#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cập nhật địa chỉ brand_office dựa trên geometry data
Task: Sử dụng dữ liệu geometry để xác định chính xác xã/phường và tỉnh/thành phố
"""

import json
import logging
import pandas as pd
import geopandas as gpd
import mysql.connector
from mysql.connector import Error
from shapely.geometry import Point, shape
import warnings
from typing import AsyncGenerator, List, Dict, Any
from gemini import Gemini
warnings.filterwarnings('ignore')
import asyncio
import time


# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/update_address.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrandOfficeAddressUpdater:
    def __init__(self, memory_limit_mb=1024):
        self.connection = None
        self.results = []
        self.batch_size = 1000
        self.processed_count = 0
        self.matched_count = 0
        self.unmatched_count = 0
        self.gemini = Gemini()  # Separate instance

        # Prepare initial prompt (không cần pre-initialize vì mỗi request tự tạo chat instance)
        self.initial_prompt = self.gemini.build_conversion_prompt()
        logger.info(f"🧠 Memory optimization enabled (separate chat instances per request)")
        
    async def process_with_memory_optimization(self, data_stream, geo_ward_data):
        """Xử lý data với memory optimization (nhận vào một stream)"""
        logger.info("🧠 Processing với memory optimization (separate Gemini instance)")
        results = await self.process_large_dataset(
            data_stream, geo_ward_data,
            output_file='exports/brand_offices_updated.csv'
        )
        return results

    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_brand_office_data(self, offset=0, limit=2000):
        """Lấy dữ liệu brand_office theo batch"""
        query = """
        SELECT id, latitude, longitude, city_id, address_old
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        and status = 2
        LIMIT %s OFFSET %s
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (limit, offset))
            results = cursor.fetchall()
            cursor.close()
            
            logger.info(f"📊 Lấy được {len(results)} records brand_office (offset: {offset})")
            return results
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu brand_office: {e}")
            return []

    async def stream_brand_office_data(self):
        """
        Tạo một generator để stream dữ liệu từ database thay vì tải tất cả.
        """
        offset = 0
        while True:
            batch_data = self.get_brand_office_data(offset, self.batch_size)
            if not batch_data:
                break

            logger.info(f"Streamed {len(batch_data)} records from database (offset: {offset})")
            yield batch_data

            await asyncio.sleep(0.01) # Small sleep to prevent blocking
            offset += self.batch_size

    def get_geo_ward_data(self):
        """Lấy dữ liệu geo_ward và trả về GeoDataFrame với spatial index"""

        query = """
        SELECT geometry, geo_province_code, province_title, ward_title, code
        FROM geo_ward
        WHERE geometry IS NOT NULL
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} geo_ward records")

            # Chuyển đổi sang GeoDataFrame
            gdf_data = []
            for row in results:
                try:
                    # Parse geometry từ JSON string
                    if isinstance(row['geometry'], str):
                        geometry_data = json.loads(row['geometry'])
                    else:
                        geometry_data = row['geometry']

                    geometry = shape(geometry_data)

                    gdf_data.append({
                        'geometry': geometry,
                        'geo_province_code': row['geo_province_code'],
                        'province_title': row['province_title'],
                        'ward_title': row['ward_title'],
                        'code': row['code']
                    })
                except Exception as e:
                    logger.warning(f"⚠️ Lỗi parse geometry cho ward {row.get('code', 'unknown')}: {e}")
                    continue

            if not gdf_data:
                logger.error("❌ Không có geometry hợp lệ nào")
                return gpd.GeoDataFrame()

            # Tạo GeoDataFrame
            gdf = gpd.GeoDataFrame(gdf_data, crs='EPSG:4326')

            # Tạo spatial index để tối ưu hóa tìm kiếm
            logger.info("🔍 Tạo spatial index cho GeoDataFrame...")
            gdf.sindex  # Trigger spatial index creation
            logger.info(f"✅ Đã tạo GeoDataFrame với {len(gdf)} records và spatial index")
            return gdf

        except Error as e:
            logger.error(f"❌ Lỗi lấy geo_ward data: {e}")
            return gpd.GeoDataFrame()
    
    def parse_geometry(self, geometry_str):
        """Parse geometry từ JSON string thành Shapely object"""
        try:
            if isinstance(geometry_str, str):
                geometry_data = json.loads(geometry_str)
            else:
                geometry_data = geometry_str
                
            return shape(geometry_data)
        except Exception as e:
            logger.warning(f"⚠️ Lỗi parse geometry: {e}")
            return None
    
    def find_ward_by_lat_lng(self, lat, lng, geo_ward_gdf):
        """Tìm ward chứa tọa độ sử dụng spatial index (tối ưu hóa)"""

        try:
            point = Point(lng, lat)

            if len(geo_ward_gdf) == 0:
                return None, 'no_data'

            logger.debug(f"🔍 Tìm kiếm spatial index trong {len(geo_ward_gdf)} wards cho tọa độ ({lat}, {lng})")

            # Sử dụng spatial index để tìm candidates nhanh chóng
            try:
                # Lấy possible matches từ spatial index
                possible_matches_idx = list(geo_ward_gdf.sindex.intersection(point.bounds))

                if not possible_matches_idx:
                    logger.debug(f"🔍 Không tìm thấy candidates từ spatial index")
                    
                    return None, 'no_match'

                logger.debug(f"🔍 Spatial index tìm được {len(possible_matches_idx)} candidates")

                # Kiểm tra chính xác các candidates
                for idx in possible_matches_idx:
                    try:
                        ward_row = geo_ward_gdf.iloc[idx]
                        geometry = ward_row.geometry

                        if geometry:
                            # Thử contains() trước (chính xác hơn)
                            if geometry.contains(point):
                                ward_dict = ward_row.to_dict()
                                
                                return ward_dict, 'contains'
                            # Fallback: intersects() (cho trường hợp point ở biên)
                            elif geometry.intersects(point):
                                ward_dict = ward_row.to_dict()
                                
                                return ward_dict, 'intersects'
                    except Exception as candidate_error:
                        logger.warning(f"⚠️ Lỗi xử lý candidate {idx}: {candidate_error}")
                        continue

                # Fallback: buffer search cho các candidates
                for idx in possible_matches_idx:
                    try:
                        ward_row = geo_ward_gdf.iloc[idx]
                        geometry = ward_row.geometry

                        if geometry and geometry.buffer(0.001).intersects(point):
                            ward_dict = ward_row.to_dict()
                            
                            return ward_dict, 'buffer'
                    except Exception as buffer_error:
                        logger.warning(f"⚠️ Lỗi buffer search candidate {idx}: {buffer_error}")
                        continue

                
                return None, 'no_match'

            except Exception as spatial_error:
                logger.warning(f"⚠️ Lỗi spatial index, fallback to linear search: {spatial_error}")
                # Fallback to linear search nếu spatial index lỗi
               
                try:
                    # Convert GeoDataFrame to records safely
                    geo_ward_records = geo_ward_gdf.to_dict('records')
                    return self.find_ward_by_lat_lng_legacy(lat, lng, geo_ward_records)                    
                    
                except Exception as fallback_error:
                    logger.error(f"❌ Lỗi fallback search: {fallback_error}")
                    
                    return None, 'error'

        except Exception as e:
            logger.warning(f"⚠️ Lỗi tìm ward tại ({lat}, {lng}): {e}")
            return None, 'error'

    def find_ward_by_lat_lng_legacy(self, lat, lng, geo_ward_data):
        """Hàm tìm kiếm legacy (linear search) để backward compatibility"""
        try:
            point = Point(lng, lat)

            logger.debug(f"🔍 Linear search trong {len(geo_ward_data)} wards cho tọa độ ({lat}, {lng})")

            for ward in geo_ward_data:
                try:
                    geometry = None

                    if isinstance(ward, dict):
                        geometry = self.parse_geometry(ward['geometry'])
                    elif hasattr(ward, 'geometry'):
                        # Pandas Series hoặc object có geometry attribute
                        geometry = ward.geometry
                    elif hasattr(ward, 'get') and callable(ward.get):
                        # Object có method get()
                        geometry = self.parse_geometry(ward.get('geometry'))
                    else:
                        # Skip nếu không thể xử lý
                        logger.debug(f"⚠️ Không thể xử lý ward type: {type(ward)}")
                        continue

                    if geometry:
                        # Thử contains() trước (chính xác hơn)
                        if geometry.contains(point):
                            return ward, 'contains'
                        # Fallback: intersects() (cho trường hợp point ở biên)
                        elif geometry.intersects(point):
                            return ward, 'intersects'
                        # Fallback: buffer cho trường hợp point ở gần đó
                        elif geometry.buffer(0.001).intersects(point):
                            return ward, 'buffer'

                except Exception as ward_error:
                    logger.debug(f"⚠️ Lỗi xử lý ward: {ward_error}")
                    continue
            return None, 'no_match'

        except Exception as e:
            logger.warning(f"⚠️ Lỗi legacy search tại ({lat}, {lng}): {e}")
            return None, 'error'

    def save_results_to_csv(self, results, filename='brand_office_updated.csv'):
        """Lưu kết quả ra CSV"""
        try:
            df = pd.DataFrame(results)
            filepath = f"exports/{filename}"
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"✅ Đã lưu {len(results)} records vào {filepath}")
            
            # Thống kê
            matched = len([r for r in results if r['status'] == 'matched'])
            unmatched = len([r for r in results if r['status'] == 'unmatched'])
            
            logger.info(f"📊 THỐNG KÊ: Matched: {matched}, Unmatched: {unmatched}")
            
        except Exception as e:
            logger.error(f"❌ Lỗi lưu CSV: {e}")

    async def process_stream(self, data_stream: AsyncGenerator[Dict[str, Any], None],
                           chunk_size: int = 50) -> AsyncGenerator[List[Dict[str, Any]], None]:
        logger.info(f"🌊 Starting stream processing (chunk_size: {chunk_size})")
        
        chunk = []
        async for item in data_stream:
            chunk.append(item)
            if len(chunk) >= chunk_size:
                results = await self._process_chunk(chunk)
                yield results
                chunk.clear()
                self.processed_count += len(results)
        if chunk:
            results = await self._process_chunk(chunk)
            yield results
            self.processed_count += len(results)
    
    async def _process_chunk(self, chunk: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process chunk with CONCURRENT execution instead of sequential"""
        chunk_start_time = time.time()
        logger.info(f"🚀 Processing chunk of {len(chunk)} items CONCURRENTLY")

        try:
            # Create tasks for concurrent execution
            async def process_single_item(item):
                try:
                    brand_office = item['brand_office']
                    ward = item['ward']
                    province = item['province']
                    ward_code = item['ward_code']
                    geo_province_code = item['geo_province_code']

                    new_address = await self.gemini.convert_address_async(
                        self.initial_prompt, brand_office, ward, province
                    )

                    return {
                        'id': brand_office.get('id', '-1'),
                        'latitude': brand_office.get('latitude', ''),
                        'longitude': brand_office.get('longitude', ''),
                        'city_id': brand_office.get('city_id', ''),
                        'geo_province_code': geo_province_code,
                        'ward_code': ward_code,
                        'original_address': brand_office.get('address_old', ''),
                        'new_address': new_address,
                        'ward': ward,
                        'province': province,
                        'status': 'success'
                    }

                except Exception as e:
                    brand_office = item.get('brand_office', {})
                    logger.warning(f"⚠️ Error processing item {brand_office.get('id', 'unknown')}: {e}")
                    return {
                        'id': brand_office.get('id', 'unknown'),
                        'latitude': brand_office.get('latitude', ''),
                        'longitude': brand_office.get('longitude', ''),
                        'city_id': brand_office.get('city_id', ''),
                        'geo_province_code': item.get('geo_province_code', ''),
                        'ward_code': item.get('ward_code', ''),
                        'original_address': brand_office.get('address_old', ''),
                        'new_address': 'ITEM_ERROR',
                        'ward': item.get('ward', ''),
                        'province': item.get('province', ''),
                        'status': 'error'
                    }

            # Execute all items CONCURRENTLY
            tasks = [process_single_item(item) for item in chunk]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle any exceptions from gather
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    item = chunk[i]
                    brand_office = item.get('brand_office', {})
                    logger.error(f"❌ Exception processing item {brand_office.get('id', 'unknown')}: {result}")
                    processed_results.append({
                        'id': brand_office.get('id', 'unknown'),
                        'latitude': brand_office.get('latitude', ''),
                        'longitude': brand_office.get('longitude', ''),
                        'city_id': brand_office.get('city_id', ''),
                        'geo_province_code': item.get('geo_province_code', ''),
                        'ward_code': item.get('ward_code', ''),
                        'original_address': brand_office.get('address_old', ''),
                        'new_address': 'EXCEPTION_ERROR',
                        'ward': item.get('ward', ''),
                        'province': item.get('province', ''),
                        'status': 'error'
                    })
                else:
                    processed_results.append(result)

            chunk_time = time.time() - chunk_start_time
            chunk_rpm = len(chunk) / (chunk_time / 60) if chunk_time > 0 else 0
            success_count = len([r for r in processed_results if r.get('status') == 'success'])

            logger.info(f"✅ Chunk completed: {len(chunk)} items in {chunk_time:.2f}s ({chunk_rpm:.1f} RPM) | Success: {success_count}/{len(chunk)}")

            return processed_results

        except Exception as e:
            logger.error(f"❌ Critical error processing chunk: {e}")
            return [
                {
                    'id': item['brand_office'].get('id', 'unknown'),
                    'latitude': item['brand_office'].get('latitude', ''),
                    'longitude': item['brand_office'].get('longitude', ''),
                    'city_id': item['brand_office'].get('city_id', ''),
                    'geo_province_code': item.get('geo_province_code', ''),
                    'ward_code': item.get('ward_code', ''),
                    'original_address': item['brand_office'].get('address_old', ''),
                    'new_address': 'CHUNK_ERROR',
                    'ward': item.get('ward', ''),
                    'province': item.get('province', ''),
                    'status': 'error'
                }
                for item in chunk
            ]
    
    async def process_large_dataset(self, data_stream: AsyncGenerator, geo_ward_data, output_file='exports/large_dataset_results.csv'):

        logger.info("🚀 Starting large dataset processing với separate Gemini instance...")

        # Initialize CSV file with headers
        headers = ['id', 'latitude', 'longitude', 'city_id', 
                'geo_province_code',  'ward_code', 'province_title', 'ward_title', 'address_old', 'new_address', 'status']
        pd.DataFrame(columns=headers).to_csv(output_file, index=False)

        total_processed = 0
        total_success = 0
        total_errors = 0

        async def processed_stream_generator():
            """Generator to perform geometry matching on the input stream"""
            async for batch in data_stream:
                for record in batch:
                    ward_info = None
                    try:
                        ward_info, _ = self.find_ward_by_lat_lng(
                            float(record['latitude']),
                            float(record['longitude']),
                            geo_ward_data
                        )
                     
                        if ward_info and isinstance(ward_info, dict):
                            # Validate required fields
                            required_fields = ['ward_title', 'province_title', 'code', 'geo_province_code']
                            if all(field in ward_info for field in required_fields):
                                yield {
                                    'brand_office': record,
                                    'ward': ward_info['ward_title'],
                                    'province': ward_info['province_title'],
                                    'ward_code': ward_info['code'],
                                    'geo_province_code': ward_info['geo_province_code']
                                }
                            else:
                                logger.warning(f"⚠️ Ward info missing required fields: {ward_info}")
                        else:
                            # No ward found or invalid ward_info
                            pass
                    except Exception as e:
                        logger.error(f"❌ Error in processed_stream_generator for record {record.get('id', 'unknown')}: {e}")
                        continue

        # Process stream in chunks - reduced chunk_size for better concurrency
        async for chunk_results in self.process_stream(processed_stream_generator(), chunk_size=50):
            # Convert to proper format
            formatted_results = []
            for result in chunk_results:
                try:
                    # Validate result structure
                    if not isinstance(result, dict):
                        logger.error(f"❌ Result is not dict: {type(result)} - {result}")
                        continue

                    # Extract brand_office data safely
                    brand_office = result.get('brand_office', {})
                    if not isinstance(brand_office, dict):
                        logger.error(f"❌ brand_office is not dict: {type(brand_office)}")
                        continue

                    formatted_result = {
                        'id': result.get('id', '-2'),
                        'latitude': result.get('latitude', ''),
                        'longitude': result.get('longitude', ''),
                        'city_id': result.get('city_id', ''),
                        'geo_province_code': result.get('geo_province_code', ''),
                        'ward_code': result.get('ward_code', ''),
                        'province_title': result.get('province', ''),
                        'ward_title': result.get('ward', ''),
                        'address_old': result.get('original_address', ''),
                        'new_address': result.get('new_address', ''),  # This might come from AI processing
                        'status': result.get('status', 'matched')  # Default to matched for geometry matches
                    }
                    formatted_results.append(formatted_result)
                except Exception as format_error:
                    logger.error(f"❌ Error formatting result: {format_error}")
                    logger.error(f"   Result: {result}")
                    continue

            # Append to CSV immediately (streaming write)
            chunk_df = pd.DataFrame(formatted_results)
            chunk_df.to_csv(output_file, mode='a', header=False, index=False)

            # Update stats - safe access
            chunk_success = len([
                r for r in chunk_results
                if isinstance(r, dict) and r.get('status') == 'success'
            ])
            chunk_errors = len(chunk_results) - chunk_success

            total_processed += len(chunk_results)
            total_success += chunk_success
            total_errors += chunk_errors

            # Log chunk stats
            logger.info(f"✅ Chunk completed: {len(chunk_results)} records | "
                       f"Success: {chunk_success} | Errors: {chunk_errors}")

        # Final stats
        success_rate = total_success / total_processed * 100 if total_processed > 0 else 0

        logger.info(f"🎉 Large dataset processing completed!")
        logger.info(f"📊 Final stats:")
        logger.info(f"   - Total processed: {total_processed}")
        logger.info(f"   - Success: {total_success} ({success_rate:.1f}%)")
        logger.info(f"   - Errors: {total_errors}")
        logger.info(f"   - Output file: {output_file}")

        return {
            'total_processed': total_processed,
            'total_success': total_success,
            'total_errors': total_errors,
            'success_rate': success_rate,
            'output_file': output_file
        }
        
    async def run(self):
        """Chạy toàn bộ process với async support"""
        try:
            logger.info("🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE")

            # Kết nối database
            self.connection = self.get_database_connection()
            if not self.connection:
                return

            # Lấy dữ liệu mapping và geo_ward
            logger.info("📊 Lấy dữ liệu mapping và geometry...")
            geo_ward_data = self.get_geo_ward_data()

            if geo_ward_data.empty:
                logger.error("❌ Không có dữ liệu geo_ward")
                return

            logger.info("" + "="*60)
            logger.info("🔄 XỬ LÝ RECORDS")
            logger.info("="*60)
            logger.info("🧠 Using memory optimization for processing")
            data_stream = self.stream_brand_office_data()
            memory_results = await self.process_with_memory_optimization(
                data_stream, geo_ward_data
            )
            logger.info(f"✅ UPDATED BRAND OFFICE ADDRESS COMPLETED! Processed: {memory_results.get('total_processed', 0)}")

            # Log final Gemini statistics
            self.gemini.log_final_stats()

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình xử lý: {e}")
        finally:
            if self.connection:
                self.connection.close()

if __name__ == "__main__":
    import asyncio
    updater = BrandOfficeAddressUpdater()
    asyncio.run(updater.run())
